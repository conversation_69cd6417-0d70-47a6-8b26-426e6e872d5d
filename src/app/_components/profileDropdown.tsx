"use client";
import React from "react";
import { signOut } from "next-auth/react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { PersonStanding, Settings2, User as UserIcon } from "lucide-react";
import type { User } from "@prisma/client";

const ProfileDropdown = ({ user }: { user: Partial<User> }) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Avatar className="border">
          <AvatarImage
            src={
              user.image ||
              `https://api.dicebear.com/7.x/identicon/svg?seed=${user?.email}`
            }
            alt="@shadcn"
          />
          <AvatarFallback>
            <Settings2 />
          </AvatarFallback>
        </Avatar>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56">
        <DropdownMenuLabel className="flex items-center gap-2">
          <UserIcon size={20} />
          {user.name}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuItem
          onClick={async () => {
            await signOut({
              callbackUrl: "/login",
              redirect: false,
            });
            window.location.href = "/login";
          }}
        >
          Log out
          <DropdownMenuShortcut className="hidden sm:inline-flex">
            ⇧⌘Q
          </DropdownMenuShortcut>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ProfileDropdown;
