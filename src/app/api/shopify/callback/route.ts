import { NextRequest, NextResponse } from "next/server";
import { ShopifyService } from "@/server/services/shopify";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get("code");
    const shop = searchParams.get("shop");
    const state = searchParams.get("state");
    const error = searchParams.get("error");

    // Handle OAuth errors
    if (error) {
      return NextResponse.redirect(
        new URL(`/shopify?error=${encodeURIComponent(error)}`, request.url)
      );
    }

    // Validate required parameters
    if (!code || !shop) {
      return NextResponse.redirect(
        new URL("/shopify?error=missing_parameters", request.url)
      );
    }

    // Extract shop name from full domain if needed
    const shopName = shop.replace(".myshopify.com", "");

    try {
      // Exchange code for access token
      const tokenResponse = await ShopifyService.getAccessToken(shopName, code);
      
      // Redirect back to Shopify page with success and token info
      const redirectUrl = new URL("/shopify", request.url);
      redirectUrl.searchParams.set("success", "true");
      redirectUrl.searchParams.set("shop", shopName);
      redirectUrl.searchParams.set("access_token", tokenResponse.access_token);
      redirectUrl.searchParams.set("scope", tokenResponse.scope);
      
      if (state) {
        redirectUrl.searchParams.set("state", state);
      }

      return NextResponse.redirect(redirectUrl);
    } catch (tokenError) {
      console.error("Error exchanging token:", tokenError);
      return NextResponse.redirect(
        new URL("/shopify?error=token_exchange_failed", request.url)
      );
    }
  } catch (error) {
    console.error("Shopify OAuth callback error:", error);
    return NextResponse.redirect(
      new URL("/shopify?error=callback_error", request.url)
    );
  }
}
