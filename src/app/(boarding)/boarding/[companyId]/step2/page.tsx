"use client";
import React, { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { Area, AreaChart } from "recharts";

import { ChartContainer } from "@/components/ui/chart";
import Stepper, { Step } from "@/components/ui/Stepper";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Progress } from "@/components/ui/progress";
import { api } from "@/trpc/react";
import { ETLJobLogStatus } from "@prisma/client";
import JobCard from "./_components/jobCard";
import { Loader } from "lucide-react";

const BoardingPage = () => {
  const router = useRouter();
  const params = useParams() as { companyId: string };
  const session = useSession();

  const [loading, setLoading] = useState(false);
  const [etlJobRunning, setEtlJobRunning] = useState(false);
  const { data: company, refetch: refetchCompany } =
    api.company.getCompany.useQuery({
      companyId: params.companyId,
    });
  const { data } = api.sse.on.useSubscription(
    {
      eventName: "etl",
      channelId: params.companyId, // Valid UUID format
      lastEventId: params.companyId,
    },
    {
      onData: async (data) => {
        console.log("data", data);
        setEtlJobRunning(true);
        if (data?.done) {
          await refetchCompany();
          setEtlJobRunning(false);
        }
      },
    },
  );
  const { mutateAsync: startEtlProcess } =
    api.quickbooks.startEtlProcess.useMutation();
  const { mutateAsync: updateBoardingState } =
    api.quickbooks.updateBoardingState.useMutation({
      onSuccess: () => {
        // refetchBoardingState();
      },
    });
  // ETL calls part

  const handleStartEtl = async (refetch = false) => {
    setEtlJobRunning(true);
    await startEtlProcess({
      companyId: params.companyId,
      trigger: "BOARDING",
      refetch: refetch,
    });
    setEtlJobRunning(false);
  };

  if (session.status === "loading") {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-bold">Loading...</h2>
          <Progress className="mt-4 w-48" value={33} />
        </div>
      </div>
    );
  }
  console.log(company);

  return (
    <div className="flex h-full items-center justify-center p-4">
      <Stepper
        initialStep={2}
        disableStepIndicators
        // onStepChange={setCurrentStep}
        nextButton={(step) => {
          const boardingJob = company?.etlJobs?.find(
            (job) => job.trigger === "BOARDING",
          );
          console.log(boardingJob, "etlJobRunning");

          if (!boardingJob) {
            return (
              <Button
                onClick={() => handleStartEtl()}
                disabled={etlJobRunning}
                className="w-full md:w-fit"
              >
                Start Process
              </Button>
            );
          }
          if (etlJobRunning)
            return (
              <Button disabled className="w-full md:w-fit">
                Loading ...
              </Button>
            );
          if (!etlJobRunning && boardingJob?.status === "RUNNING")
            return (
              <div className="w-full items-center gap-3 md:flex md:w-fit">
                <div className="mb-1 text-xs text-red-500 md:text-sm">
                  Something must've went wrong!
                </div>

                <Button
                  onClick={() => handleStartEtl(true)}
                  disabled={etlJobRunning}
                  className="w-full text-xs md:w-fit md:text-sm"
                >
                  Resume Process
                </Button>
              </div>
            );

          return (
            <>
              {boardingJob?.status === "FAILED" ? (
                <div className="grid w-full grid-cols-2 gap-3 md:block md:w-fit md:space-x-2">
                  <Button
                    variant={"outline"}
                    onClick={() => {
                      handleStartEtl(true);
                    }}
                    disabled={etlJobRunning}
                    className="text-xs md:text-sm"
                  >
                    Retry
                  </Button>
                  <Button
                    className="bg-orange-400 text-xs md:text-sm"
                    onClick={async () => {
                      await updateBoardingState({
                        companyId: params.companyId,
                        boardingStep: 3,
                      });
                      router.push(`/boarding/${params.companyId}/step3`);
                    }}
                  >
                    continue with errors
                  </Button>
                </div>
              ) : (
                <div className="grid w-full grid-cols-2 gap-3 md:block md:w-fit md:space-x-2">
                  <Button
                    variant={"outline"}
                    onClick={() => {
                      handleStartEtl(true);
                    }}
                    disabled={etlJobRunning}
                  >
                    Revalidate
                  </Button>
                  <Button
                    disabled={loading}
                    onClick={async () => {
                      setLoading(true);
                      await updateBoardingState({
                        companyId: params.companyId,
                        boardingStep: 3,
                      });
                      router.push(`/boarding/${params.companyId}/step3`);
                    }}
                  >
                    {loading ? <Loader className="animate-spin" /> : "Next"}
                  </Button>
                </div>
              )}
            </>
          );
        }}
        backButton={(step) => {
          return (
            <Button
              disabled={etlJobRunning}
              className="w-full text-xs md:w-fit"
              size={"sm"}
              variant={"outline"}
              onClick={() => router.push("/boarding")}
            >
              Change Account?
            </Button>
          );
        }}
      >
        <Step>
          <></>
        </Step>

        <Step>
          <h2 className="text-xl font-bold">QuickBooks Connected</h2>
          <p className="my-4">
            Your QuickBooks account has been successfully connected.
          </p>
          <div className="space-y-2">
            <div>
              Now we need to extract your{" "}
              <span className="font-bold">
                {
                  session.data?.user?.UserCompanies.find(
                    (c) => c?.companyId === params.companyId,
                  )?.Company?.CompanyName
                }
              </span>{" "}
              data to get started.
            </div>

            <div>
              Here You Will see the Status of the information extracted from
              quickbooks:
            </div>
          </div>

          <div className="mt-6 space-y-3">
            {[
              "ACCOUNTS",
              "BILLS",
              "CLASSES",
              "VENDORS",
              "CUSTOMERS",
              "INVOICES",
              "TERMS",
            ].map((entity) => {
              if (etlJobRunning) {
                const eStat = data?.[entity] as
                  | {
                      status: ETLJobLogStatus;
                      success: boolean;
                      count: number;
                      message: string;
                      error?: string;
                    }
                  | undefined;

                return (
                  <JobCard
                    key={entity}
                    data={{
                      count: eStat?.count || 0,
                      entity: entity,
                      error: eStat?.error || "",
                      message: eStat?.message || "",
                      status: (eStat?.status || "IDLE") as any,
                    }}
                  />
                );
              }
              const eStat = company?.etlJobs
                .find((job) => job.trigger === "BOARDING")
                ?.logs.find((log) => log.entity === entity);
              return (
                <JobCard
                  key={entity}
                  data={{
                    count: eStat?.count || 0,
                    entity: entity,
                    error: eStat?.error || "",
                    message: eStat?.message || "",
                    status: (eStat?.status || "IDLE") as any,
                  }}
                />
              );
            })}
          </div>
        </Step>

        <Step>
          <></>
        </Step>
        <Step>
          <></>
        </Step>
        <Step>
          <></>
        </Step>
        <Step>
          <></>
        </Step>
      </Stepper>
    </div>
  );
};

export default BoardingPage;
