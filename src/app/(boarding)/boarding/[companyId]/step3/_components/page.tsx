"use client";
import { useState, useMemo } from "react";
import { useParams, useRouter } from "next/navigation";
import { Area, Line, AreaChart, LineChart } from "recharts";

import { ChartContainer } from "@/components/ui/chart";
import Stepper, { Step } from "@/components/ui/Stepper";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { api } from "@/trpc/react";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Loader, ChevronDown, ChevronUp } from "lucide-react";
import type { RevenueStreamsReportI } from "tst";
import SplitText from "@/components/ui/SplitText";

const Step3 = ({
  revenueStreams,
}: {
  revenueStreams: RevenueStreamsReportI | undefined;
}) => {
  const router = useRouter();
  const params = useParams() as { companyId: string };
  const [loading, setLoading] = useState(false);
  const [expandedAccounts, setExpandedAccounts] = useState<Set<string>>(
    new Set(),
  );

  const { mutateAsync: updateBoardingState } =
    api.quickbooks.updateBoardingState.useMutation();

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  /**
   * Formats currency values with proper locale and currency symbol
   */
  const formatCurrency = (amount: number, currency = "AED") => {
    return `${amount.toLocaleString("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })} ${currency}`;
  };

  /**
   * Safely compares two dates for sorting
   */
  const compareDates = (a: { date: string }, b: { date: string }) => {
    const dateA = new Date(a.date).getTime();
    const dateB = new Date(b.date).getTime();
    return dateA - dateB;
  };

  // ============================================================================
  // DATA PROCESSING
  // ============================================================================

  /**
   * Processes and combines last 12 months data from all accounts
   * Creates a unified monthly chart data for the main overview
   */
  const last12MonthsData = useMemo(() => {
    if (!revenueStreams?.accounts) return [];

    // Calculate date range for last 12 months
    const now = new Date();
    const twelveMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 11, 1);

    // Map to store combined monthly totals across all accounts
    const monthlyTotals = new Map<string, number>();

    // Process each account's monthly data
    revenueStreams.accounts.forEach((account) => {
      account.total_monthly?.forEach((monthData) => {
        if (monthData?.date) {
          const monthDate = new Date(monthData.date);
          if (monthDate >= twelveMonthsAgo && !isNaN(monthDate.getTime())) {
            const monthKey = monthDate.toISOString().substring(0, 7); // YYYY-MM format
            const currentTotal = monthlyTotals.get(monthKey) || 0;
            monthlyTotals.set(monthKey, currentTotal + (monthData.amount || 0));
          }
        }
      });
    });

    // Convert to array and sort by date
    return Array.from(monthlyTotals.entries())
      .map(([date, total_sales]) => ({
        date,
        total_sales,
        month: new Date(date + "-01").toLocaleDateString("en-US", {
          month: "short",
          year: "numeric",
        }),
      }))
      .sort(compareDates);
  }, [revenueStreams]);

  /**
   * Gets last 12 months data for a specific account
   * Used for individual account mini-charts
   */
  const getAccountLast12Months = (account: any) => {
    if (!account?.total_monthly) return [];

    const now = new Date();
    const twelveMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 11, 1);

    return (
      account.total_monthly
        // .filter((monthData: any) => {
        //   if (!monthData?.date) return false;
        //   const date = new Date(monthData.date);
        //   return date >= twelveMonthsAgo && !isNaN(date.getTime());
        // })
        .map((monthData: any) => ({
          date: monthData.date,
          amount: monthData.amount || 0,
          month: new Date(monthData.date).toLocaleDateString("en-US", {
            month: "short",
          }),
        }))
        .sort(compareDates)
    );
  };

  /**
   * Sorts account classes from highest to lowest amount
   */
  const getSortedClasses = (classes: any[]) => {
    if (!classes) return [];
    return [...classes].sort((a, b) => Math.abs(b.amount) - Math.abs(a.amount));
  };

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  /**
   * Toggles the expansion state of an account card
   */
  const toggleAccountExpansion = (accountName: string) => {
    const newExpanded = new Set(expandedAccounts);
    if (newExpanded.has(accountName)) {
      newExpanded.delete(accountName);
    } else {
      newExpanded.add(accountName);
    }
    setExpandedAccounts(newExpanded);
  };

  // ============================================================================
  // RENDER COMPONENT
  // ============================================================================

  return (
    <div className="flex h-full items-center justify-center p-4">
      <Stepper
        disableStepIndicators
        initialStep={3}
        nextButton={(step) => {
          return (
            <Button
              disabled={loading}
              className="w-full md:w-fit"
              onClick={async () => {
                setLoading(true);
                await updateBoardingState({
                  companyId: params.companyId,
                  boardingStep: 4,
                });
                router.push(`/boarding/${params.companyId}/step4`);
              }}
            >
              {loading ? <Loader className="animate-spin" /> : "Next"}
            </Button>
          );
        }}
        backButton={(step) => {
          return (
            <Button
              variant={"outline"}
              className="w-full md:w-fit"
              onClick={() => {
                const step = Number(
                  window.location.pathname.toString().slice(-1),
                );
                router.push(`/boarding/${params.companyId}/step${step - 1}`);
              }}
            >
              Back
            </Button>
          );
        }}
      >
        <Step>
          <></>
        </Step>

        <Step>
          <></>
        </Step>

        <Step>
          {/* ============================================================================ */}
          {/* PAGE HEADER */}
          {/* ============================================================================ */}
          <div className="space-y-2">
            <h2 className="text-lg font-bold tracking-tight md:text-2xl">
              Review - Revenue Streams
            </h2>
            <p className="text-muted-foreground text-sm md:text-lg">
              Review revenue streams before proceeding
            </p>
          </div>

          {/* ============================================================================ */}
          {/* MAIN OVERVIEW CHART - Last 12 Months Combined Data */}
          {/* ============================================================================ */}
          <div className="mt-8 rounded-lg border-t-8 p-4">
            <div className="mb-2 flex justify-between">
              <p className="font-semibold">Last 12 months average income</p>
              <p className="font-bold">
                {revenueStreams?.avg_monthly_total_income.toLocaleString(
                  "en-US",
                  {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  },
                )}{" "}
                {revenueStreams?.Currency}
              </p>
            </div>
            <SplitText
              text={`Hi, my name is Wilfredo. I scanned your income accounts to understand where your money's coming from.`}
              className="text-center text-sm font-semibold md:text-xl"
              delay={10}
              animationFrom={{ opacity: 0, transform: "translate3d(0,10px,0)" }}
              animationTo={{ opacity: 1, transform: "translate3d(0,0,0)" }}
              easing="easeOutCubic"
              threshold={0.2}
              rootMargin="-50px"
              onLetterAnimationComplete={() => {}}
            />
            <br />
            <SplitText
              text={`This helps me track your active revenue streams so I can give you a clear view of your cash flow, help you prioritize payments, and plan ahead with confidence.`}
              className="text-center text-sm font-semibold md:text-xl"
              delay={30}
              animationFrom={{ opacity: 0, transform: "translate3d(0,10px,0)" }}
              animationTo={{ opacity: 1, transform: "translate3d(0,0,0)" }}
              easing="easeOutCubic"
              threshold={0.2}
              rootMargin="-50px"
              onLetterAnimationComplete={() => {}}
            />
            <br />
            <SplitText
              text={`Some accounts might need a quick cleanup if they're outdated or unused - don’t worry, I’ll flag anything that looks off.`}
              className="text-center text-sm font-semibold md:text-xl"
              delay={50}
              animationFrom={{ opacity: 0, transform: "translate3d(0,10px,0)" }}
              animationTo={{ opacity: 1, transform: "translate3d(0,0,0)" }}
              easing="easeOutCubic"
              threshold={0.2}
              rootMargin="-50px"
              onLetterAnimationComplete={() => {}}
            />
          </div>

          {/* ============================================================================ */}
          {/* INDIVIDUAL ACCOUNT CARDS */}
          {/* ============================================================================ */}
          <div className="space-y-3">
            <ScrollArea className="h-[calc(100dvh-55dvh)]">
              <div className="space-y-4 overflow-y-auto pb-4">
                {revenueStreams?.accounts?.map((account, index) => {
                  if (!account.avg_monthly) return null;
                  const isExpanded = expandedAccounts.has(account.account_name);
                  const accountLast12Months = getAccountLast12Months(account);
                  const sortedClasses = getSortedClasses(account.classes || []);

                  return (
                    <Card
                      key={index}
                      className="hover:border-primary/50 overflow-hidden border-slate-200 transition-all duration-200 hover:shadow-md"
                    >
                      <CardContent className="px-4">
                        <div className="space-y-4">
                          {/* ============================================================================ */}
                          {/* ACCOUNT HEADER - Clickable to expand/collapse */}
                          {/* ============================================================================ */}
                          <div
                            className="flex cursor-pointer items-center justify-between"
                            onClick={() =>
                              toggleAccountExpansion(account.account_name)
                            }
                          >
                            <div className="flex w-full justify-between space-y-1">
                              <div className="flex items-center gap-2">
                                <h4 className="font-medium">
                                  {account.account_name} - (
                                  {account.account_type})
                                </h4>
                                {isExpanded ? (
                                  <ChevronUp className="text-muted-foreground h-4 w-4" />
                                ) : (
                                  <ChevronDown className="text-muted-foreground h-4 w-4" />
                                )}
                              </div>
                              <div className="flex items-baseline gap-1.5">
                                <span className="text-lg font-bold">
                                  {formatCurrency(
                                    account.avg_monthly || 0,
                                    revenueStreams?.Currency,
                                  )}
                                </span>
                                <span className="text-muted-foreground text-sm">
                                  Average / Month
                                </span>
                              </div>
                            </div>
                          </div>

                          {isExpanded && (
                            <div className="space-y-3 border-t pt-2">
                              <h5 className="text-muted-foreground text-sm font-medium">
                                Revenue Classes (Sorted by Amount)
                              </h5>
                              <div className="space-y-3">
                                {sortedClasses.map((classItem, classIndex) => {
                                  const percentage =
                                    (Math.abs(classItem.amount) /
                                      Math.abs(account.total_amount)) *
                                    100;

                                  return (
                                    <div key={classIndex} className="space-y-2">
                                      {/* Class Header with Name and Amount */}
                                      <div className="flex items-center justify-between">
                                        <span className="text-sm font-medium">
                                          {classItem.className ||
                                            "Not specified"}
                                        </span>
                                        <div className="flex items-center gap-2">
                                          <span className="text-sm font-bold">
                                            {formatCurrency(
                                              classItem.amount,
                                              revenueStreams?.Currency,
                                            )}
                                          </span>
                                          <span className="text-muted-foreground text-xs">
                                            ({percentage.toFixed(1)}%)
                                          </span>
                                        </div>
                                      </div>
                                      {/* Progress Bar showing percentage */}
                                      <Progress
                                        value={percentage}
                                        className="h-2"
                                      />
                                    </div>
                                  );
                                })}
                              </div>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </ScrollArea>
          </div>
        </Step>
        <Step>
          <></>
        </Step>
        <Step>
          <></>
        </Step>
        <Step>
          <></>
        </Step>
      </Stepper>
    </div>
  );
};

export default Step3;
