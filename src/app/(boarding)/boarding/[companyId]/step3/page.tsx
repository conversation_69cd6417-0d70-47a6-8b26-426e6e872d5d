import React from "react";
import Step3 from "./_components/page";
import { api } from "@/trpc/server";

const page = async ({ params }: { params: Promise<{ companyId: string }> }) => {
  const { companyId } = await params;
  const revenueStreams = await api.quickbooks.revenueStreamsReport({
    companyId: companyId,
  });

  return <Step3 revenueStreams={revenueStreams as any} />;
};

export default page;

