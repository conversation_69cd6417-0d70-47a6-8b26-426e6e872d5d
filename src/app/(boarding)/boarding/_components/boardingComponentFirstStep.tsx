"use client";
import { useState, useEffect, useMemo } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { signIn, useSession } from "next-auth/react";
import { CheckCircle, Building2, Plus } from "lucide-react";

import Stepper, { Step } from "@/components/ui/Stepper";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { api } from "@/trpc/react";
import type { User, UserCompany } from "@prisma/client";
import type { CompanyI } from "@/types/company";

const BoardingComponentFirstStep = ({
  user,
  companies,
}: {
  user: User;
  companies: (UserCompany & { Company: CompanyI })[];
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data, status } = useSession();
  const [selectedCompany, setSelectedCompany] = useState<string | null>(
    companies.length === 1 && companies
      ? companies?.[0]?.companyId || null
      : null,
  );
  const [authLoading, setAuthLoading] = useState(false);

  const { mutate: getAuthUrl } = api.quickbooks.getAuthUrl.useMutation({
    onSuccess: (url) => {
      window.location.href = url;
    },
  });
  const { mutateAsync: authenticateQuickBooks } =
    api.quickbooks.authenticateQuickBooks.useMutation();

  const handleAuthQuickBooks = async (
    params: Record<"code" | "state" | "realmId", string>,
  ) => {
    if (authLoading) return;

    setAuthLoading(true);
    try {
      const result = await authenticateQuickBooks({
        url: window.location.href,
        ...params,
      });

      if (result.success) {
        router.push("/boarding");
      }
    } catch (error) {
      console.error("QuickBooks authentication error:", error);
      alert("QuickBooks authentication failed");
    } finally {
      setAuthLoading(false);
    }
  };

  useEffect(() => {
    if (status !== "authenticated") return;

    const params = Object.fromEntries(searchParams.entries());

    if ("code" in params && data?.user.userId) {
      handleAuthQuickBooks(
        params as Record<"code" | "state" | "realmId", string>,
      );
    }
  }, [searchParams, status]);

  if (status === "loading") {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-bold">Loading...</h2>
          <Progress className="mt-4 w-48" value={33} />
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full items-center justify-center p-4">
      <Stepper
        initialStep={1}
        disableStepIndicators
        nextButton={(step) => {
          if (step === 1) {
            return (
              <Button
                disabled={!selectedCompany}
                className="w-full md:w-fit"
                onClick={() => {
                  if (!selectedCompany) {
                    router.refresh();
                  } else {
                    router.push(`/boarding/${selectedCompany}/step2`);
                  }
                }}
              >
                Next
              </Button>
            );
          }
        }}
      >
        <Step>
          <div className="min-h-fit max-w-3xl px-2">
            <h2 className="text-2xl font-bold">Welcome to Wilfredo</h2>
            <p className="text-muted-foreground my-4">
              You need to connect your QuickBooks account to import your
              financial data. Click the button below to start the connection
              process.
            </p>

            {companies.length > 0 && (
              <div className="mb-6 space-y-4">
                <h3 className="text-lg font-medium">
                  Your QuickBooks Accounts
                </h3>

                {companies?.length ? (
                  <p className="text-muted-foreground text-sm">
                    Select a company to continue or add a new one
                  </p>
                ) : null}

                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  {companies.map((company) => (
                    <Card
                      key={company.companyId}
                      className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                        selectedCompany === company.companyId
                          ? "border-primary ring-primary/20 border-2 ring-2"
                          : "border-border hover:border-primary/50 border"
                      }`}
                      onClick={() => setSelectedCompany(company.companyId)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="mb-2 flex items-center gap-2">
                              <Building2 className="text-muted-foreground h-5 w-5" />
                              <h4 className="line-clamp-1 font-medium">
                                {company.Company.CompanyName}
                              </h4>
                            </div>
                            <p className="text-muted-foreground text-xs">
                              QuickBooks Account
                            </p>
                          </div>
                          {selectedCompany === company.companyId && (
                            <CheckCircle className="text-primary h-5 w-5" />
                          )}
                        </div>
                        <Badge variant="outline" className="mt-3">
                          Connected
                        </Badge>
                      </CardContent>
                    </Card>
                  ))}

                  <Card
                    className="border-muted-foreground/30 bg-muted/30 hover:border-primary/50 cursor-pointer border border-dashed transition-all duration-200 hover:shadow-sm"
                    onClick={() => getAuthUrl()}
                  >
                    <CardContent className="flex h-full flex-col items-center justify-center p-4 text-center">
                      <div className="bg-muted mb-2 rounded-full p-2">
                        <Plus className="text-muted-foreground h-5 w-5" />
                      </div>
                      <h4 className="font-medium">Add New Company</h4>
                      <p className="text-muted-foreground mt-1 text-xs">
                        Connect another QuickBooks account
                      </p>
                    </CardContent>
                  </Card>
                </div>
              </div>
            )}

            {companies.length === 0 && (
              <div className="mt-6 w-full">
                <Button
                  size="lg"
                  className="gap-2"
                  onClick={() => getAuthUrl()}
                >
                  <Plus className="h-4 w-4" />
                  Connect QuickBooks Account
                </Button>
              </div>
            )}
          </div>
        </Step>

        <Step>
          <></>
        </Step>
        <Step>
          <></>
        </Step>
        <Step>
          <></>
        </Step>
        <Step>
          <></>
        </Step>
        <Step>
          <></>
        </Step>
      </Stepper>
    </div>
  );
};

export default BoardingComponentFirstStep;
