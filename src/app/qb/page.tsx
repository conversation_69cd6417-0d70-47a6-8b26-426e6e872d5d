"use client";
import { Button } from "@/components/ui/button";
import { api } from "@/trpc/react";
import React from "react";
import { DynamicTable } from "../_components/dynamic-table";

const page = () => {
  const { data } = api.quickbooks.qbReport.useQuery({
    companyId: "cmb9fr6lp13vdpbut2k6suw5m",
    url: "TransactionListWithSplits?start_date=2024-05-01&end_date=2025-05-29",
    // companyId: "cmav02o7g0008pbjfyny976cz",
  });
  console.log(data);

  return (
    <div className="p-10">
      {JSON.stringify(data || {})}
      {/* <div className="h-screen overflow-y-auto">
      </div> */}
      {/* <DynamicTable data={data?.filter((d) => d.totalMonthlyAvg) as any} /> */}
      {/* <Button onClick={() => mutate()}>go</Button> */}
    </div>
  );
};

export default page;
