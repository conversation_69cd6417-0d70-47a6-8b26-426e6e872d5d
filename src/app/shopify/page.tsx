"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { api } from "@/trpc/react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Loader2,
  ShoppingBag,
  Store,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import type { ShopifyProduct } from "@/server/services/shopify";

export default function ShopifyPage() {
  const searchParams = useSearchParams();
  const [shopName, setShopName] = useState("");
  const [accessToken, setAccessToken] = useState("");
  const [isConnected, setIsConnected] = useState(false);
  const [products, setProducts] = useState<ShopifyProduct[]>([]);
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // tRPC mutations and queries
  const getAuthUrl = api.shopify.getAuthUrl.useMutation();
  const getProducts = api.shopify.getProducts.useQuery(
    {
      shop: shopName,
      accessToken: accessToken,
      limit: 10,
    },
    {
      enabled: false, // Only run when manually triggered
    },
  );
  const testConnection = api.shopify.testConnection.useQuery(
    {
      shop: shopName,
      accessToken: accessToken,
    },
    {
      enabled: false, // Only run when manually triggered
    },
  );

  // Handle OAuth callback
  useEffect(() => {
    const success = searchParams.get("success");
    const shop = searchParams.get("shop");
    const token = searchParams.get("access_token");
    const error = searchParams.get("error");
    const details = searchParams.get("details");

    console.log("URL params:", {
      success,
      shop: !!shop,
      token: !!token,
      error,
      details,
    });

    if (error) {
      const errorMessage = details ? `${error}: ${details}` : error;
      setError(`Authentication failed: ${errorMessage}`);
    } else if (success === "true" && shop && token) {
      setShopName(shop);
      setAccessToken(token);
      setIsConnected(true);
      setError("");

      // Clear URL parameters
      window.history.replaceState({}, document.title, "/shopify");
    }
  }, [searchParams]);

  const handleConnect = async () => {
    if (!shopName.trim()) {
      setError("Please enter a shop name");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      const result = await getAuthUrl.mutateAsync({
        shop: shopName.trim(),
        state: "shopify_auth",
      });

      // Redirect to Shopify OAuth
      window.location.href = result.authUrl;
    } catch (err) {
      setError("Failed to generate authorization URL");
      setIsLoading(false);
    }
  };

  const handleTestConnection = async () => {
    if (!isConnected) {
      setError("Please connect to Shopify first");
      return;
    }

    try {
      await testConnection.refetch();
    } catch (err) {
      setError("Connection test failed");
    }
  };

  const handleFetchProducts = async () => {
    if (!isConnected) {
      setError("Please connect to Shopify first");
      return;
    }

    try {
      const result = await getProducts.refetch();
      if (result.data) {
        setProducts(result.data.products);
        setError("");
      }
    } catch (err) {
      setError("Failed to fetch products");
    }
  };

  const handleDisconnect = () => {
    setShopName("");
    setAccessToken("");
    setIsConnected(false);
    setProducts([]);
    setError("");
  };

  return (
    <div className="container mx-auto max-w-6xl p-6">
      <div className="mb-8">
        <h1 className="mb-2 flex items-center gap-2 text-3xl font-bold">
          <Store className="h-8 w-8" />
          Shopify Integration
        </h1>
        <p className="text-muted-foreground">
          Connect to your Shopify store and manage your products
        </p>
      </div>

      {/* Connection Status */}
      {isConnected && (
        <Alert className="mb-6 border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            Successfully connected to <strong>{shopName}.myshopify.com</strong>
          </AlertDescription>
        </Alert>
      )}

      {/* Error Display */}
      {error && (
        <Alert className="mb-6 border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Connection Panel */}
        <Card>
          <CardHeader>
            <CardTitle>Store Connection</CardTitle>
            <CardDescription>
              Connect to your Shopify store to access products and data
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="shop-name" className="text-sm font-medium">
                Shop Name
              </label>
              <div className="flex gap-2">
                <Input
                  id="shop-name"
                  placeholder="your-shop-name"
                  value={shopName}
                  onChange={(e) => setShopName(e.target.value)}
                  disabled={isConnected}
                />
                <span className="text-muted-foreground flex items-center text-sm">
                  .myshopify.com
                </span>
              </div>
            </div>

            {!isConnected ? (
              <Button
                onClick={handleConnect}
                disabled={isLoading || !shopName.trim()}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Connecting...
                  </>
                ) : (
                  "Connect to Shopify"
                )}
              </Button>
            ) : (
              <div className="space-y-2">
                <Button
                  onClick={handleTestConnection}
                  variant="outline"
                  className="w-full"
                  disabled={testConnection.isLoading}
                >
                  {testConnection.isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Testing...
                    </>
                  ) : (
                    "Test Connection"
                  )}
                </Button>
                <Button
                  onClick={handleDisconnect}
                  variant="destructive"
                  className="w-full"
                >
                  Disconnect
                </Button>
              </div>
            )}

            {testConnection.data && (
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-800">
                  <div className="space-y-1">
                    <div>{testConnection.data.message}</div>
                    {testConnection.data.shopDomain && (
                      <div className="text-xs">
                        Domain: {testConnection.data.shopDomain} | Currency:{" "}
                        {testConnection.data.currency}
                      </div>
                    )}
                  </div>
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Products Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShoppingBag className="h-5 w-5" />
              Products
            </CardTitle>
            <CardDescription>
              Fetch and view products from your Shopify store
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={handleFetchProducts}
              disabled={!isConnected || getProducts.isLoading}
              className="w-full"
            >
              {getProducts.isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Fetching Products...
                </>
              ) : (
                "Fetch Products"
              )}
            </Button>

            {products.length > 0 && (
              <div className="space-y-2">
                <p className="text-muted-foreground text-sm">
                  Found {products.length} products
                </p>
                <Separator />
              </div>
            )}

            {getProducts.error && (
              <Alert className="border-orange-200 bg-orange-50">
                <AlertCircle className="h-4 w-4 text-orange-600" />
                <AlertDescription className="text-orange-800">
                  <div className="space-y-2">
                    <div className="font-medium">Products fetch failed</div>
                    <div className="text-xs">
                      {getProducts.error.message.includes(
                        "merchant approval",
                      ) ? (
                        <>
                          This Shopify app needs merchant approval for the
                          read_products scope. To fix this:
                          <ol className="mt-1 list-inside list-decimal space-y-1">
                            <li>Go to your Shopify admin → Apps</li>
                            <li>Find this app and click "Review"</li>
                            <li>Approve the requested permissions</li>
                            <li>Or reinstall the app to grant permissions</li>
                          </ol>
                        </>
                      ) : (
                        getProducts.error.message
                      )}
                    </div>
                  </div>
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Products List */}
      {products.length > 0 && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Product List</CardTitle>
            <CardDescription>Products from your Shopify store</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              {products.map((product) => (
                <Card key={product.id} className="overflow-hidden">
                  {product.image && (
                    <div className="aspect-square overflow-hidden">
                      <img
                        src={product.image.src}
                        alt={product.image.alt || product.title}
                        className="h-full w-full object-cover"
                      />
                    </div>
                  )}
                  <CardContent className="p-4">
                    <h3 className="mb-2 line-clamp-2 text-sm font-semibold">
                      {product.title}
                    </h3>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-xs">
                          Vendor
                        </span>
                        <span className="text-xs">{product.vendor}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-xs">
                          Type
                        </span>
                        <span className="text-xs">{product.product_type}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground text-xs">
                          Status
                        </span>
                        <Badge
                          variant={
                            product.status === "active"
                              ? "default"
                              : "secondary"
                          }
                          className="text-xs"
                        >
                          {product.status}
                        </Badge>
                      </div>
                      {product.variants.length > 0 && (
                        <div className="flex items-center justify-between">
                          <span className="text-muted-foreground text-xs">
                            Price
                          </span>
                          <span className="text-xs font-medium">
                            ${product.variants[0]?.price}
                          </span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
