"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Copy } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";

// User's schema generation function
type Primitive = string | number | boolean | null;

function isObject(value: any): value is Record<string, any> {
  return value !== null && typeof value === "object" && !Array.isArray(value);
}

/**
 * Generate a merged schema object containing all possible keys
 * with default values based on their types.
 * @param data Array of arbitrary objects
 * @returns A single object with defaulted keys and nested schemas
 */
function generateSchema(data: any[]): any {
  const schema: any = {};
  const objectValues: Record<string, any[]> = {};
  const arrayValues: Record<string, any[]> = {};
  const primitiveTypes: Record<string, string> = {};

  // Collect values by key
  data.forEach((item) => {
    if (!isObject(item)) return;
    Object.entries(item).forEach(([key, val]) => {
      if (Array.isArray(val)) {
        arrayValues[key] = (arrayValues[key] || []).concat(val);
      } else if (isObject(val)) {
        objectValues[key] = (objectValues[key] || []).concat(val);
      } else {
        primitiveTypes[key] = typeof val;
      }
    });
  });

  // Union of all keys encountered
  const keys = new Set<string>([
    ...Object.keys(primitiveTypes),
    ...Object.keys(objectValues),
    ...Object.keys(arrayValues),
  ]);

  keys.forEach((key) => {
    // Handle primitives
    if (primitiveTypes[key]) {
      const type = primitiveTypes[key];
      let defaultVal: Primitive;
      switch (type) {
        case "string":
          defaultVal = "";
          break;
        case "number":
          defaultVal = 0;
          break;
        case "boolean":
          defaultVal = false;
          break;
        default:
          defaultVal = null;
      }
      schema[key] = defaultVal;
    }

    // Handle nested objects
    if (objectValues[key]) {
      schema[key] = generateSchema(objectValues[key]);
    }

    // Handle arrays
    if (arrayValues[key]) {
      const items = arrayValues[key];
      // If array of objects, merge their schemas
      if (items.every((it) => isObject(it))) {
        schema[key] = [generateSchema(items as any[])];
      } else {
        // Array of primitives
        const sampleType = typeof items.find((it) => it != null) || "string";
        let defaultVal: Primitive;
        switch (sampleType) {
          case "string":
            defaultVal = "";
            break;
          case "number":
            defaultVal = 0;
            break;
          case "boolean":
            defaultVal = false;
            break;
          default:
            defaultVal = null;
        }
        schema[key] = [defaultVal];
      }
    }
  });

  return schema;
}

// Helper functions for interface generation
function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

function mergeObjects(objects: any[]): any {
  const result: any = {};
  objects.forEach((obj) => {
    if (isObject(obj)) {
      Object.entries(obj).forEach(([key, val]) => {
        if (!(key in result)) {
          result[key] = val;
        } else if (isObject(val) && isObject(result[key])) {
          result[key] = { ...result[key], ...val };
        }
      });
    }
  });
  return result;
}

/**
 * Generate TypeScript interfaces from a JSON object or array.
 * @param json The JSON data (object or array)
 * @param rootName The name of the root interface
 * @returns A string containing TypeScript interfaces
 */
function generateInterface(json: any, rootName = "Root"): string {
  const interfaces = new Map<string, string>();

  function parse(obj: any, name: string) {
    if (!isObject(obj)) return;
    const lines: string[] = [];
    Object.entries(obj).forEach(([key, val]) => {
      let tsType: string;
      if (Array.isArray(val)) {
        if (val.length === 0) {
          tsType = "any[]";
        } else {
          const first = val[0];
          if (isObject(first)) {
            const childName = `${name}${capitalize(key)}`;
            parse(mergeObjects(val), childName);
            tsType = `${childName}[]`;
          } else {
            tsType = `${typeof first}[]`;
          }
        }
      } else if (isObject(val)) {
        const childName = `${name}${capitalize(key)}`;
        parse(val, childName);
        tsType = childName;
      } else {
        tsType = typeof val;
      }
      lines.push(`  ${key}?: ${tsType};`);
    });
    interfaces.set(
      name,
      `interface ${name} {
${lines.join("\n")}
}`,
    );
  }

  const rootData = Array.isArray(json) ? mergeObjects(json) : json;
  parse(rootData, rootName);

  return Array.from(interfaces.values()).join("\n\n");
}
function generatePrismaModel(json: any, rootName = "Root"): string {
  const interfaces = new Map<string, string>();

  function parse(obj: any, name: string) {
    if (!isObject(obj)) return;
    const lines: string[] = [];
    Object.entries(obj).forEach(([key, val]) => {
      let tsType: string;
      if (Array.isArray(val)) {
        if (val.length === 0) {
          tsType = "any[]";
        } else {
          const first = val[0];
          if (isObject(first)) {
            const childName = `${name}${capitalize(key)}`;
            parse(mergeObjects(val), childName);
            tsType = `${childName}[]`;
          } else {
            tsType = `${typeof first}[]`;
          }
        }
      } else if (isObject(val)) {
        const childName = `Json`;
        parse(val, childName);
        tsType = childName;
      } else {
        tsType = typeof val;
      }
      lines.push(`  ${key} ${capitalize(tsType)}?`);
    });
    interfaces.set(
      name,
      `
${lines.join("\n")}
`,
    );
  }

  const rootData = Array.isArray(json) ? mergeObjects(json) : json;
  parse(rootData, rootName);

  return Array.from(interfaces.values()).join("\n\n");
}

function generatePrismaModelText(interfaceString: string): string {
  const lines = interfaceString.split("\n").map((line) => line.trim());
  const modelLines: string[] = [];

  for (const line of lines) {
    const match = line.match(/^(\w+)\??: ([\w\[\]]+);$/);
    if (!match) continue;

    const [, name, type] = match;

    let prismaType: string;

    switch (type) {
      case "string":
        prismaType = "String";
        break;
      case "number":
        prismaType = "Float";
        break;
      case "boolean":
        prismaType = "Boolean";
        break;
      default:
        prismaType = "Json";
    }

    // Add optional marker (?) to Prisma field
    modelLines.push(`${name} ${prismaType}?`);
  }

  return modelLines.join("\n");
}

export default function JsonTemplateGenerator() {
  const [jsonInput, setJsonInput] = useState("");
  const [templateOutput, setTemplateOutput] = useState("");
  const [interfaceOutput, setInterfaceOutput] = useState("");
  const [prismaOutput, setPrismaOutput] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("json");

  const generateTemplate = () => {
    try {
      setError(null);

      // Parse the input JSON
      const jsonArray = JSON.parse(jsonInput);

      if (!Array.isArray(jsonArray)) {
        throw new Error("Input must be a JSON array");
      }

      // Use the user's generateSchema function
      const template = generateSchema(jsonArray);

      // Format the output with 2-space indentation
      setTemplateOutput(JSON.stringify(template, null, 2));

      // Generate TypeScript interfaces
      const interfaces = generateInterface(template, "Root");
      setInterfaceOutput(interfaces);
      const splits = interfaces.split("interface");

      const prismaModel = generatePrismaModelText(
        `interface${splits[splits.length - 1]}`,
      );
      console.log(prismaModel);

      setPrismaOutput(prismaModel);

      // Switch to JSON tab by default
      setActiveTab("json");
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "An unknown error occurred",
      );
      setTemplateOutput("");
      setInterfaceOutput("");
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="container mx-auto max-w-5xl p-4">
      <h1 className="mb-6 text-center text-2xl font-bold">
        JSON Template Generator
      </h1>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Input JSON Array</CardTitle>
            <CardDescription>
              Paste your JSON array here to generate a template with all
              possible keys
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Textarea
              value={jsonInput}
              onChange={(e) => setJsonInput(e.target.value)}
              placeholder="Paste your JSON array here..."
              className="max-h-96 min-h-[400px] font-mono text-sm"
            />
            <Button onClick={generateTemplate} className="mt-4 w-full">
              Generate Template
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Output</span>
              {templateOutput && activeTab === "json" && (
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => copyToClipboard(templateOutput)}
                  title="Copy to clipboard"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              )}
              {interfaceOutput && activeTab === "typescript" && (
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => copyToClipboard(interfaceOutput)}
                  title="Copy to clipboard"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              )}
            </CardTitle>
            <CardDescription>
              View the generated template or TypeScript interfaces
            </CardDescription>
          </CardHeader>
          <CardContent>
            {error ? (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            ) : (
              <Tabs
                value={activeTab}
                onValueChange={setActiveTab}
                className="w-full"
              >
                <TabsList className="mb-4 grid w-full grid-cols-3">
                  <TabsTrigger value="json">JSON Template</TabsTrigger>
                  <TabsTrigger value="typescript">
                    TypeScript Interfaces
                  </TabsTrigger>
                  <TabsTrigger value="prisma">Prisma</TabsTrigger>
                </TabsList>
                <TabsContent value="json">
                  <Textarea
                    value={templateOutput}
                    readOnly
                    className="max-h-96 min-h-[400px] font-mono text-sm"
                    placeholder="Template will appear here..."
                  />
                </TabsContent>
                <TabsContent value="typescript">
                  <Textarea
                    value={interfaceOutput}
                    readOnly
                    className="max-h-96 min-h-[400px] font-mono text-sm"
                    placeholder="TypeScript interfaces will appear here..."
                  />
                </TabsContent>
                <TabsContent value="prisma">
                  <Textarea
                    value={prismaOutput}
                    readOnly
                    className="max-h-96 min-h-[400px] font-mono text-sm"
                    placeholder="TypeScript interfaces will appear here..."
                  />
                </TabsContent>
              </Tabs>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
