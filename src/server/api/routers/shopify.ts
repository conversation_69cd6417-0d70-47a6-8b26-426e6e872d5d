import { z } from "zod";
import { TRPCError } from "@trpc/server";
import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
} from "@/server/api/trpc";
import { ShopifyService } from "@/server/services/shopify";

export const shopifyRouter = createTRPCRouter({
  /**
   * Generate Shopify OAuth authorization URL
   */
  getAuthUrl: publicProcedure
    .input(
      z.object({
        shop: z.string().min(1, "Shop name is required"),
        state: z.string().optional(),
      }),
    )
    .mutation(({ input }) => {
      try {
        const authUrl = ShopifyService.getAuthorizationUrl(
          input.shop,
          input.state,
        );
        return { authUrl };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to generate authorization URL",
          cause: error,
        });
      }
    }),

  /**
   * Exchange authorization code for access token
   */
  exchangeToken: publicProcedure
    .input(
      z.object({
        shop: z.string().min(1, "Shop name is required"),
        code: z.string().min(1, "Authorization code is required"),
      }),
    )
    .mutation(async ({ input }) => {
      try {
        const tokenResponse = await ShopifyService.getAccessToken(
          input.shop,
          input.code,
        );
        return tokenResponse;
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to exchange authorization code for access token",
          cause: error,
        });
      }
    }),

  /**
   * Get orders (for sales data)
   */
  getOrders: publicProcedure
    .input(
      z.object({
        shop: z.string().min(1, "Shop name is required"),
        accessToken: z.string().min(1, "Access token is required"),
        limit: z.number().min(1).max(250).default(50),
        status: z.string().default("any"),
      }),
    )
    .query(async ({ input }) => {
      console.log("tRPC getOrders called with:", input);
      try {
        const orders = await ShopifyService.getOrders(
          input.shop,
          input.accessToken,
          input.limit,
          input.status,
        );
        return orders;
      } catch (error) {
        console.error("tRPC getOrders error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Failed to fetch orders from Shopify: ${error instanceof Error ? error.message : "Unknown error"}`,
          cause: error,
        });
      }
    }),

  /**
   * Get sales analytics
   */
  getSalesAnalytics: publicProcedure
    .input(
      z.object({
        shop: z.string().min(1, "Shop name is required"),
        accessToken: z.string().min(1, "Access token is required"),
      }),
    )
    .query(async ({ input }) => {
      console.log("tRPC getSalesAnalytics called with:", input);
      try {
        const analytics = await ShopifyService.getSalesAnalytics(
          input.shop,
          input.accessToken,
        );
        return analytics;
      } catch (error) {
        console.error("tRPC getSalesAnalytics error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Failed to fetch sales analytics from Shopify: ${error instanceof Error ? error.message : "Unknown error"}`,
          cause: error,
        });
      }
    }),

  /**
   * Test connection to Shopify store
   */
  testConnection: publicProcedure
    .input(
      z.object({
        shop: z.string().min(1, "Shop name is required"),
        accessToken: z.string().min(1, "Access token is required"),
      }),
    )
    .query(async ({ input }) => {
      console.log("tRPC testConnection called with:", input);

      try {
        // Try to fetch shop info to test the connection (doesn't require special permissions)
        const result = await ShopifyService.getShop(
          input.shop,
          input.accessToken,
        );

        return {
          success: true,
          message: `Successfully connected to ${result.shop.name}`,
          shopName: result.shop.name,
          shopDomain: result.shop.domain,
          currency: result.shop.currency,
        };
      } catch (error) {
        console.error("tRPC testConnection error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Failed to connect to Shopify store: ${error instanceof Error ? error.message : "Unknown error"}`,
          cause: error,
        });
      }
    }),

  /**
   * Get shop information
   */
  getShop: publicProcedure
    .input(
      z.object({
        shop: z.string().min(1, "Shop name is required"),
        accessToken: z.string().min(1, "Access token is required"),
      }),
    )
    .query(async ({ input }) => {
      console.log("tRPC getShop called with:", input);
      try {
        const result = await ShopifyService.getShop(
          input.shop,
          input.accessToken,
        );
        return result;
      } catch (error) {
        console.error("tRPC getShop error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Failed to fetch shop information: ${error instanceof Error ? error.message : "Unknown error"}`,
          cause: error,
        });
      }
    }),

  /**
   * Check granted scopes
   */
  checkScopes: publicProcedure
    .input(
      z.object({
        shop: z.string().min(1, "Shop name is required"),
        accessToken: z.string().min(1, "Access token is required"),
      }),
    )
    .query(async ({ input }) => {
      console.log("tRPC checkScopes called with:", input);
      try {
        const result = await ShopifyService.checkScopes(
          input.shop,
          input.accessToken,
        );
        return result;
      } catch (error) {
        console.error("tRPC checkScopes error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Failed to check scopes: ${error instanceof Error ? error.message : "Unknown error"}`,
          cause: error,
        });
      }
    }),

  /**
   * Get locations (alternative to products)
   */
  getLocations: publicProcedure
    .input(
      z.object({
        shop: z.string().min(1, "Shop name is required"),
        accessToken: z.string().min(1, "Access token is required"),
      }),
    )
    .query(async ({ input }) => {
      console.log("tRPC getLocations called with:", input);
      try {
        const result = await ShopifyService.getLocations(
          input.shop,
          input.accessToken,
        );
        return result;
      } catch (error) {
        console.error("tRPC getLocations error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Failed to fetch locations: ${error instanceof Error ? error.message : "Unknown error"}`,
          cause: error,
        });
      }
    }),

  /**
   * Get webhooks (alternative to products)
   */
  getWebhooks: publicProcedure
    .input(
      z.object({
        shop: z.string().min(1, "Shop name is required"),
        accessToken: z.string().min(1, "Access token is required"),
      }),
    )
    .query(async ({ input }) => {
      console.log("tRPC getWebhooks called with:", input);
      try {
        const result = await ShopifyService.getWebhooks(
          input.shop,
          input.accessToken,
        );
        return result;
      } catch (error) {
        console.error("tRPC getWebhooks error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Failed to fetch webhooks: ${error instanceof Error ? error.message : "Unknown error"}`,
          cause: error,
        });
      }
    }),
});
