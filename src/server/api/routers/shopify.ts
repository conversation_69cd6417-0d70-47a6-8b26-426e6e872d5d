import { z } from "zod";
import { TRPCError } from "@trpc/server";
import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
} from "@/server/api/trpc";
import { ShopifyService } from "@/server/services/shopify";

export const shopifyRouter = createTRPCRouter({
  /**
   * Generate Shopify OAuth authorization URL
   */
  getAuthUrl: publicProcedure
    .input(
      z.object({
        shop: z.string().min(1, "Shop name is required"),
        state: z.string().optional(),
      }),
    )
    .mutation(({ input }) => {
      try {
        const authUrl = ShopifyService.getAuthorizationUrl(
          input.shop,
          input.state,
        );
        return { authUrl };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to generate authorization URL",
          cause: error,
        });
      }
    }),

  /**
   * Exchange authorization code for access token
   */
  exchangeToken: publicProcedure
    .input(
      z.object({
        shop: z.string().min(1, "Shop name is required"),
        code: z.string().min(1, "Authorization code is required"),
      }),
    )
    .mutation(async ({ input }) => {
      try {
        const tokenResponse = await ShopifyService.getAccessToken(
          input.shop,
          input.code,
        );
        return tokenResponse;
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to exchange authorization code for access token",
          cause: error,
        });
      }
    }),

  /**
   * Fetch products from Shopify store
   */
  getProducts: publicProcedure
    .input(
      z.object({
        shop: z.string().min(1, "Shop name is required"),
        accessToken: z.string().min(1, "Access token is required"),
        limit: z.number().min(1).max(250).default(50),
      }),
    )
    .query(async ({ input }) => {
      try {
        const products = await ShopifyService.getProducts(
          input.shop,
          input.accessToken,
          input.limit,
        );
        return products;
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch products from Shopify",
          cause: error,
        });
      }
    }),

  /**
   * Test connection to Shopify store
   */
  testConnection: publicProcedure
    .input(
      z.object({
        shop: z.string().min(1, "Shop name is required"),
        accessToken: z.string().min(1, "Access token is required"),
      }),
    )
    .query(async ({ input }) => {
      console.log("testConnection", input);

      try {
        // Try to fetch a small number of products to test the connection
        const result = await ShopifyService.getProducts(
          input.shop,
          input.accessToken,
          1,
        );

        return {
          success: true,
          message: "Successfully connected to Shopify store",
          productCount: result.products.length,
        };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to connect to Shopify store",
          cause: error,
        });
      }
    }),
});
