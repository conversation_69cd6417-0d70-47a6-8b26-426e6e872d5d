import { createCallerFactory, createTRPCRouter } from "@/server/api/trpc";
import { userRouter } from "./routers/user";
import { quickbooksRouter } from "./routers/quickbooks";
import { shopifyRouter } from "./routers/shopify";
import { companyRouter } from "./routers/company";
import { etlRouter } from "./routers/etl";
import { sseRouter } from "./routers/sse";
import { fileRouter } from "./routers/file";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  user: userRouter,
  company: companyRouter,
  etl: etlRouter,
  quickbooks: quickbooksRouter,
  shopify: shopifyRouter,
  sse: sseRouter,
  file: fileRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter);
