import OAuthClient from "intuit-oauth";

import { env } from "@/env";

// Create OAuth client with environment variables
const oauthClient = new OAuthClient({
  clientId: env.QB_CLIENT_ID,
  clientSecret: env.QB_CLIENT_SECRET,
  environment: env.QB_ENV, // 'sandbox' or 'production'
  // redirectUri: "https://034tlzz5-3000.euw.devtunnels.ms/boarding",
  redirectUri: "https://wilfredo.io/boarding",
});

type QBAuthRes = {
  id_token: string;
  expires_in: number;
  token_type: string;
  access_token: string;
  refresh_token: string;
  x_refresh_token_expires_in: number;
};

export class QuickBooksService {
  /**
   * Get authorization URL for QuickBooks OAuth
   */
  static getAuthorizationUrl() {
    return oauthClient.authorizeUri({
      scope: [
        OAuthClient.scopes.Accounting,
        OAuthClient.scopes.OpenId,
        OAuthClient.scopes.Email,
        OAuthClient.scopes.Phone,
      ],
    });
  }

  static getOauthClient(): OAuthClient {
    return oauthClient;
  }

  /**
   * Handle OAuth callback and create token
   */

  static async handleCallback(url: string) {
    try {
      const authResponse = (await oauthClient.createToken(url)).json;
      console.log("authResponse");
      console.log(authResponse);
      const tokenData = authResponse;

      return {
        ...tokenData,
      } as QBAuthRes;
    } catch (error) {
      console.error("Error creating token:", error);
      throw error;
    }
  }
}
