import { env } from "@/env";

export interface ShopifyProduct {
  id: number;
  title: string;
  body_html: string;
  vendor: string;
  product_type: string;
  created_at: string;
  handle: string;
  updated_at: string;
  published_at: string;
  template_suffix: string | null;
  published_scope: string;
  tags: string;
  status: string;
  admin_graphql_api_id: string;
  variants: ShopifyVariant[];
  options: ShopifyOption[];
  images: ShopifyImage[];
  image: ShopifyImage | null;
}

export interface ShopifyVariant {
  id: number;
  product_id: number;
  title: string;
  price: string;
  sku: string;
  position: number;
  inventory_policy: string;
  compare_at_price: string | null;
  fulfillment_service: string;
  inventory_management: string;
  option1: string;
  option2: string | null;
  option3: string | null;
  created_at: string;
  updated_at: string;
  taxable: boolean;
  barcode: string | null;
  grams: number;
  image_id: number | null;
  weight: number;
  weight_unit: string;
  inventory_item_id: number;
  inventory_quantity: number;
  old_inventory_quantity: number;
  requires_shipping: boolean;
  admin_graphql_api_id: string;
}

export interface ShopifyOption {
  id: number;
  product_id: number;
  name: string;
  position: number;
  values: string[];
}

export interface ShopifyImage {
  id: number;
  product_id: number;
  position: number;
  created_at: string;
  updated_at: string;
  alt: string | null;
  width: number;
  height: number;
  src: string;
  variant_ids: number[];
  admin_graphql_api_id: string;
}

export interface ShopifyAuthResponse {
  access_token: string;
  scope: string;
}

export class ShopifyService {
  private static readonly SCOPES = [
    "read_products",
    "read_orders",
    "read_customers",
    "read_inventory",
  ];

  /**
   * Generate Shopify OAuth authorization URL
   */
  static getAuthorizationUrl(shop: string, state?: string): string {
    const scopes = this.SCOPES.join(",");
    const redirectUri = `${process.env.NEXTAUTH_URL || "http://localhost:3001"}/api/shopify/callback`;

    const params = new URLSearchParams({
      client_id: env.SHOPIFY_CLIENT_ID,
      scope: scopes,
      redirect_uri: redirectUri,
      state: state || "",
    });

    return `https://${shop}.myshopify.com/admin/oauth/authorize?${params.toString()}`;
  }

  /**
   * Exchange authorization code for access token
   */
  static async getAccessToken(
    shop: string,
    code: string,
  ): Promise<ShopifyAuthResponse> {
    console.log("getting access token");

    const redirectUri = `${process.env.NEXTAUTH_URL || "http://localhost:3001"}/api/shopify/callback`;

    const response = await fetch(
      `https://${shop}.myshopify.com/admin/oauth/access_token`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          client_id: env.SHOPIFY_CLIENT_ID,
          client_secret: env.SHOPIFY_CLIENT_SECRET,
          code,
          redirect_uri: redirectUri,
        }),
      },
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Shopify access token error:", response.status, errorText);
      throw new Error(
        `Failed to get access token: ${response.statusText} - ${errorText}`,
      );
    }

    return response.json() as Promise<ShopifyAuthResponse>;
  }

  /**
   * Fetch products from Shopify store
   */
  static async getProducts(
    shop: string,
    accessToken: string,
    limit = 50,
  ): Promise<{ products: ShopifyProduct[] }> {
    console.log(`Fetching products for shop: ${shop}, limit: ${limit}`);

    const response = await fetch(
      `https://${shop}.myshopify.com/admin/api/2024-01/products.json?limit=${limit}`,
      {
        headers: {
          "X-Shopify-Access-Token": accessToken,
          "Content-Type": "application/json",
        },
      },
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Shopify products error:", response.status, errorText);
      throw new Error(
        `Failed to fetch products: ${response.statusText} - ${errorText}`,
      );
    }

    const data = await response.json();
    console.log(`Successfully fetched ${data.products?.length || 0} products`);
    return data as { products: ShopifyProduct[] };
  }

  /**
   * Verify webhook (for future use)
   */
  static verifyWebhook(data: string, hmacHeader: string): boolean {
    const crypto = require("crypto");
    const hmac = crypto.createHmac("sha256", env.SHOPIFY_CLIENT_SECRET);
    hmac.update(data, "utf8");
    const calculatedHmac = hmac.digest("base64");

    return crypto.timingSafeEqual(
      Buffer.from(calculatedHmac, "base64"),
      Buffer.from(hmacHeader, "base64"),
    );
  }
}
