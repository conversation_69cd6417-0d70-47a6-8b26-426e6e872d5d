import { env } from "@/env";

export interface ShopifyProduct {
  id: number;
  title: string;
  body_html: string;
  vendor: string;
  product_type: string;
  created_at: string;
  handle: string;
  updated_at: string;
  published_at: string;
  template_suffix: string | null;
  published_scope: string;
  tags: string;
  status: string;
  admin_graphql_api_id: string;
  variants: ShopifyVariant[];
  options: ShopifyOption[];
  images: ShopifyImage[];
  image: ShopifyImage | null;
}

export interface ShopifyVariant {
  id: number;
  product_id: number;
  title: string;
  price: string;
  sku: string;
  position: number;
  inventory_policy: string;
  compare_at_price: string | null;
  fulfillment_service: string;
  inventory_management: string;
  option1: string;
  option2: string | null;
  option3: string | null;
  created_at: string;
  updated_at: string;
  taxable: boolean;
  barcode: string | null;
  grams: number;
  image_id: number | null;
  weight: number;
  weight_unit: string;
  inventory_item_id: number;
  inventory_quantity: number;
  old_inventory_quantity: number;
  requires_shipping: boolean;
  admin_graphql_api_id: string;
}

export interface ShopifyOption {
  id: number;
  product_id: number;
  name: string;
  position: number;
  values: string[];
}

export interface ShopifyImage {
  id: number;
  product_id: number;
  position: number;
  created_at: string;
  updated_at: string;
  alt: string | null;
  width: number;
  height: number;
  src: string;
  variant_ids: number[];
  admin_graphql_api_id: string;
}

export interface ShopifyAuthResponse {
  access_token: string;
  scope: string;
}

export interface ShopifyShop {
  id: number;
  name: string;
  email: string;
  domain: string;
  province: string;
  country: string;
  address1: string;
  zip: string;
  city: string;
  source: string;
  phone: string;
  latitude: number;
  longitude: number;
  primary_locale: string;
  address2: string;
  created_at: string;
  updated_at: string;
  country_code: string;
  country_name: string;
  currency: string;
  customer_email: string;
  timezone: string;
  iana_timezone: string;
  shop_owner: string;
  money_format: string;
  money_with_currency_format: string;
  weight_unit: string;
  province_code: string;
  taxes_included: boolean;
  auto_configure_tax_inclusivity: boolean;
  tax_shipping: boolean;
  county_taxes: boolean;
  plan_display_name: string;
  plan_name: string;
  has_discounts: boolean;
  has_gift_cards: boolean;
  myshopify_domain: string;
  google_apps_domain: string;
  google_apps_login_enabled: boolean;
  money_in_emails_format: string;
  money_with_currency_in_emails_format: string;
  eligible_for_payments: boolean;
  requires_extra_payments_agreement: boolean;
  password_enabled: boolean;
  has_storefront: boolean;
  finances: boolean;
  primary_location_id: number;
  cookie_consent_level: string;
  visitor_tracking_consent_preference: string;
  checkout_api_supported: boolean;
  multi_location_enabled: boolean;
  setup_required: boolean;
  pre_launch_enabled: boolean;
  enabled_presentment_currencies: string[];
}

export class ShopifyService {
  private static readonly SCOPES = [
    "read_products",
    "write_products",
    "read_orders",
    "read_customers",
    "read_inventory",
  ];

  /**
   * Generate Shopify OAuth authorization URL
   */
  static getAuthorizationUrl(shop: string, state?: string): string {
    const scopes = this.SCOPES.join(",");
    const redirectUri = `${"http://localhost:3001"}/api/shopify/callback`;

    const params = new URLSearchParams({
      client_id: env.SHOPIFY_CLIENT_ID,
      scope: scopes,
      redirect_uri: redirectUri,
      state: state || "",
    });

    return `https://${shop}.myshopify.com/admin/oauth/authorize?${params.toString()}`;
  }

  /**
   * Exchange authorization code for access token
   */
  static async getAccessToken(
    shop: string,
    code: string,
  ): Promise<ShopifyAuthResponse> {
    console.log("getting access token");

    const redirectUri = `${"http://localhost:3001"}/api/shopify/callback`;

    const response = await fetch(
      `https://${shop}.myshopify.com/admin/oauth/access_token`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          client_id: env.SHOPIFY_CLIENT_ID,
          client_secret: env.SHOPIFY_CLIENT_SECRET,
          code,
          redirect_uri: redirectUri,
        }),
      },
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Shopify access token error:", response.status, errorText);
      throw new Error(
        `Failed to get access token: ${response.statusText} - ${errorText}`,
      );
    }

    return response.json() as Promise<ShopifyAuthResponse>;
  }

  /**
   * Fetch products from Shopify store
   */
  static async getProducts(
    shop: string,
    accessToken: string,
    limit = 50,
  ): Promise<{ products: ShopifyProduct[] }> {
    console.log(`Fetching products for shop: ${shop}, limit: ${limit}`);

    const response = await fetch(
      `https://${shop}.myshopify.com/admin/api/2025-04/products.json?limit=${limit}`,
      {
        headers: {
          "X-Shopify-Access-Token": accessToken,
          "Content-Type": "application/json",
        },
      },
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Shopify products error:", response.status, errorText);

      // Handle specific error cases
      if (response.status === 403) {
        if (errorText.includes("merchant approval")) {
          throw new Error(
            "This action requires merchant approval for read_products scope. Please reinstall the app or contact the store owner to approve the required permissions.",
          );
        }
        throw new Error(`Access denied: ${errorText}`);
      }

      if (response.status === 401) {
        throw new Error("Invalid access token. Please reconnect to Shopify.");
      }

      throw new Error(
        `Failed to fetch products: ${response.statusText} - ${errorText}`,
      );
    }

    const data = await response.json();
    console.log(`Successfully fetched ${data.products?.length || 0} products`);
    return data as { products: ShopifyProduct[] };
  }

  /**
   * Get shop information (doesn't require special permissions)
   */
  static async getShop(
    shop: string,
    accessToken: string,
  ): Promise<{ shop: ShopifyShop }> {
    console.log(`Fetching shop info for: ${shop}`);

    const response = await fetch(
      `https://${shop}.myshopify.com/admin/api/2025-04/shop.json`,
      {
        headers: {
          "X-Shopify-Access-Token": accessToken,
          "Content-Type": "application/json",
        },
      },
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Shopify shop info error:", response.status, errorText);

      if (response.status === 401) {
        throw new Error("Invalid access token. Please reconnect to Shopify.");
      }

      throw new Error(
        `Failed to fetch shop info: ${response.statusText} - ${errorText}`,
      );
    }

    const data = await response.json();
    console.log(`Successfully fetched shop info for: ${data.shop?.name}`);
    return data as { shop: ShopifyShop };
  }

  /**
   * Verify webhook (for future use)
   */
  static verifyWebhook(data: string, hmacHeader: string): boolean {
    const crypto = require("crypto");
    const hmac = crypto.createHmac("sha256", env.SHOPIFY_CLIENT_SECRET);
    hmac.update(data, "utf8");
    const calculatedHmac = hmac.digest("base64");

    return crypto.timingSafeEqual(
      Buffer.from(calculatedHmac, "base64"),
      Buffer.from(hmacHeader, "base64"),
    );
  }
}
