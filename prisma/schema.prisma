// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
    provider = "prisma-client-js"
}

datasource db {
    provider = "postgresql"

    url = env("DATABASE_URL")
}

// example ID
// id        Int      @id @default(autoincrement() || cuid())

enum Providers {
    QuickBooks
}

enum UserCompanyRole {
    owner
    admin
    member
}

model User {
    userId        String        @id @default(cuid())
    name          String?
    email         String        @unique
    image         String?
    password      String?
    verified      Boolean?      @default(true)
    createdAt     DateTime      @default(now())
    updatedAt     DateTime      @updatedAt
    Invitations   Invitation[]
    UserCompanies UserCompany[]
}

model QbCompany {
    companyId                      String   @id @default(cuid())
    realmId                        String
    authInfo                       Json
    // QB fields
    CompanyName                    String?
    LegalName                      String?
    CompanyStartDate               String?
    FiscalYearStartMonth           String?
    Country                        String?
    SupportedLanguages             String?
    DefaultTimeZone                String?
    domain                         String?
    sparse                         Boolean?
    Id                             String?
    SyncToken                      String?
    CompanyAddr                    Json?
    CustomerCommunicationAddr      Json?
    LegalAddr                      Json?
    CustomerCommunicationEmailAddr Json?
    PrimaryPhone                   Json?
    Email                          Json?
    WebAddr                        Json?
    MetaData                       Json?
    NameValue                      Json?
    // QB fields

    CompanyUsers UserCompany[]
    etlJobs      ETLJob[]
    Invitations  Invitation[]

    Bills       QbBill[]
    QbVendors   QbVendor[]
    QbAccounts  QbAccount[]
    QbInvoices  QbInvoice[]
    QbTerms     QbTerm[]
    QbCustomers QbCustomer[]
    QbClasses   QbClass[]
    createdAt   DateTime     @default(now())
    updatedAt   DateTime     @updatedAt
}

model Invitation {
    invitationId String  @id @default(cuid())
    email        String
    active       Boolean

    userId    String
    invitedBy User   @relation(fields: [userId], references: [userId])

    companyId String
    company   QbCompany @relation(fields: [companyId], references: [companyId])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model UserCompany {
    userCompanyId    String            @id @default(cuid())
    role             UserCompanyRole   @default(owner)
    userId           String
    User             User              @relation(fields: [userId], references: [userId])
    companyId        String
    Company          QbCompany         @relation(fields: [companyId], references: [companyId])
    createdAt        DateTime          @default(now())
    updatedAt        DateTime          @updatedAt
    UserCompanyState UserCompanyState?
}

model UserCompanyState {
    userCompanyStateId String      @id @default(cuid())
    userCompanyId      String      @unique
    boardingStep       Int
    UserCompany        UserCompany @relation(fields: [userCompanyId], references: [userCompanyId])
    createdAt          DateTime    @default(now())
    updatedAt          DateTime    @updatedAt
}

model QbVendor {
    vendorId String @id @default(cuid())

    Balance          Float?
    Vendor1099       Boolean?
    domain           String?
    sparse           Boolean?
    Id               String?
    SyncToken        String?
    DisplayName      String?
    PrintOnCheckName String?
    Active           Boolean?
    V4IDPseudonym    String?
    AcctNum          String?
    GivenName        String?
    FamilyName       String?
    TaxIdentifier    String?
    CompanyName      String?
    BillRate         Float?
    Title            String?
    MiddleName       String?
    Suffix           String?
    CurrencyRef      Json?
    MetaData         Json?
    BillAddr         Json?
    PrimaryPhone     Json?
    PrimaryEmailAddr Json?
    WebAddr          Json?
    Mobile           Json?
    Fax              Json?
    TermRef          Json?
    error            String? // Error message if extraction failed

    companyId String
    Company   QbCompany @relation(fields: [companyId], references: [companyId])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@unique([vendorId, companyId])
}

model QbBill {
    billId               String   @id @default(cuid())
    // QB fields
    DueDate              String?
    Balance              Float?
    domain               String?
    sparse               Boolean?
    Id                   String?
    SyncToken            String?
    TxnDate              String?
    TotalAmt             Float?
    PrivateNote          String?
    VendorAddr           Json?
    MetaData             Json?
    CurrencyRef          Json?
    VendorRef            Json?
    APAccountRef         Json?
    SalesTermRef         Json?
    Line                 Json?
    LinkedTxn            Json?
    DocNumber            String?
    GlobalTaxCalculation String?
    TxnTaxDetail         Json?
    DepartmentRef        Json?
    ExchangeRate         Float?
    error                String? // Error message if extraction failed
    // QB fields

    companyId String
    Company   QbCompany @relation(fields: [companyId], references: [companyId])
    createdAt DateTime  @default(now())
    updatedAt DateTime  @updatedAt

    // @@unique([billId, companyId])
}

model QbAccount {
    accountId                     String   @id @default(cuid())
    // QB fields
    Name                          String?
    SubAccount                    Boolean?
    FullyQualifiedName            String?
    Active                        Boolean?
    Classification                String?
    AccountType                   String?
    AccountSubType                String?
    CurrentBalance                Float?
    CurrentBalanceWithSubAccounts Float?
    domain                        String?
    sparse                        Boolean?
    Id                            String?
    SyncToken                     String?
    CurrencyRef                   Json?
    MetaData                      Json?
    ParentRef                     Json?
    Description                   String?
    AcctNum                       String?
    error                         String? // Error message if extraction failed
    // QB fields

    companyId String
    Company   QbCompany @relation(fields: [companyId], references: [companyId])
    createdAt DateTime  @default(now())
    updatedAt DateTime  @updatedAt
}

model QbInvoice {
    invoiceId                    String   @id @default(cuid())
    // QB fields
    AllowIPNPayment              Boolean?
    AllowOnlinePayment           Boolean?
    AllowOnlineCreditCardPayment Boolean?
    AllowOnlineACHPayment        Boolean?
    domain                       String?
    sparse                       Boolean?
    Id                           String?
    SyncToken                    String?
    DocNumber                    String?
    TxnDate                      String?
    FreeFormAddress              Boolean?
    DueDate                      String?
    TotalAmt                     Float?
    ApplyTaxAfterDiscount        Boolean?
    PrintStatus                  String?
    EmailStatus                  String?
    Balance                      Float?
    PrivateNote                  String?
    MetaData                     Json?
    CurrencyRef                  Json?
    TxnTaxDetail                 Json?
    CustomerRef                  Json?
    CustomerMemo                 Json?
    BillAddr                     Json?
    ShipAddr                     Json?
    SalesTermRef                 Json?
    BillEmail                    Json?
    DeliveryInfo                 Json?
    CustomField                  Json?
    LinkedTxn                    Json?
    Line                         Json?
    GlobalTaxCalculation         String?
    DepartmentRef                Json?
    ClassRef                     Json?
    EInvoiceStatus               String?
    ShipDate                     String?
    ShipMethodRef                Json?
    ExchangeRate                 Float?
    HomeTotalAmt                 Float?
    error                        String? // Error message if extraction failed
    // QB fields

    companyId String
    Company   QbCompany @relation(fields: [companyId], references: [companyId])
    createdAt DateTime  @default(now())
    updatedAt DateTime  @updatedAt
}

model QbTerm {
    termId           String   @id @default(cuid())
    // QB fields
    Name             String?
    Active           Boolean?
    Type             String?
    DueDays          Float?
    DiscountDays     Float?
    domain           String?
    sparse           Boolean?
    Id               String?
    SyncToken        String?
    MetaData         Json?
    DayOfMonthDue    Float?
    DueNextMonthDays Float?
    error            String? // Error message if extraction failed
    // QB fields

    companyId String
    Company   QbCompany @relation(fields: [companyId], references: [companyId])
    createdAt DateTime  @default(now())
    updatedAt DateTime  @updatedAt
}

model QbCustomer {
    customerId              String   @id @default(cuid())
    // QB fields
    Taxable                 Boolean?
    Job                     Boolean?
    BillWithParent          Boolean?
    Balance                 Float?
    BalanceWithJobs         Float?
    PreferredDeliveryMethod String?
    IsProject               Boolean?
    ClientEntityId          String?
    domain                  String?
    sparse                  Boolean?
    Id                      String?
    SyncToken               String?
    GivenName               String?
    FamilyName              String?
    FullyQualifiedName      String?
    CompanyName             String?
    DisplayName             String?
    PrintOnCheckName        String?
    Active                  Boolean?
    V4IDPseudonym           String?
    Level                   Float?
    MiddleName              String?
    BillAddr                Json?
    ShipAddr                Json?
    CurrencyRef             Json?
    MetaData                Json?
    PrimaryPhone            Json?
    PrimaryEmailAddr        Json?
    Mobile                  Json?
    Fax                     Json?
    WebAddr                 Json?
    ParentRef               Json?
    Notes                   String?
    SalesTermRef            Json?
    PaymentMethodRef        Json?
    Title                   String?
    AlternatePhone          Json?
    error                   String? // Error message if extraction failed
    // QB fields

    companyId String
    Company   QbCompany @relation(fields: [companyId], references: [companyId])
    createdAt DateTime  @default(now())
    updatedAt DateTime  @updatedAt
}

model QbClass {
    classId            String   @id @default(cuid())
    // QB fields
    FullyQualifiedName String?
    domain             String?
    Name               String?
    SyncToken          String?
    SubClass           Boolean?
    sparse             Boolean?
    Active             Boolean?
    Id                 String?
    MetaData           Json?
    ParentRef          Json?
    error              String? // Error message if extraction failed
    // QB fields

    companyId String
    Company   QbCompany @relation(fields: [companyId], references: [companyId])
    createdAt DateTime  @default(now())
    updatedAt DateTime  @updatedAt
}

enum ETLJobStatus {
    RUNNING
    COMPLETED
    FAILED
}

enum ETLJobTrigger {
    MANUAL
    SCHEDULED
    USER
    BOARDING
    SYNC
}

model ETLJob {
    etlJobId String        @id @default(cuid())
    trigger  ETLJobTrigger
    status   ETLJobStatus  @default(RUNNING)

    completedAt DateTime?
    errorAt     DateTime?

    companyId String
    company   QbCompany @relation(fields: [companyId], references: [companyId])

    logs      ETLJobLog[]
    startedAt DateTime    @default(now())
    updatedAt DateTime    @updatedAt
}

enum ETLJobEntity {
    VENDORS
    CUSTOMERS
    INVOICES
    BILLS
    ACCOUNTS
    TERMS
    CLASSES
}

enum ETLJobLogStatus {
    RUNNING
    COMPLETED
    FAILED
}

model ETLJobLog {
    etlJobLogId String          @id @default(cuid())
    entity      ETLJobEntity
    message     String?
    status      ETLJobLogStatus @default(RUNNING)
    count       Int?
    error       String?
    etlJobId    String
    etlJob      ETLJob          @relation(fields: [etlJobId], references: [etlJobId])
    // auto increment on update
    // retryCount Int    @default(0)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}
